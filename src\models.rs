use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use utoipa::ToSchema;

/// 通用的推理请求结构
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct InferenceRequest {
    /// 输入数据，键为输入名称，值为多维数组数据
    pub inputs: HashMap<String, TensorData>,
    /// 可选的输出名称列表，如果为空则返回所有输出
    #[serde(default)]
    pub output_names: Vec<String>,
}

/// 推理响应结构
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct InferenceResponse {
    /// 输出数据，键为输出名称，值为多维数组数据
    pub outputs: HashMap<String, TensorData>,
    /// 推理耗时（毫秒）
    pub inference_time_ms: f64,
    /// 请求ID
    pub request_id: String,
}

/// 张量数据结构
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, ToSchema)]
pub struct TensorData {
    /// 数据类型
    pub dtype: String,
    /// 形状
    pub shape: Vec<i64>,
    /// 数据（扁平化的一维数组）
    pub data: Vec<f32>,
}

/// 模型信息响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ModelInfo {
    /// 模型名称
    pub name: String,
    /// 模型路径
    pub path: String,
    /// 输入信息
    pub inputs: Vec<TensorInfo>,
    /// 输出信息
    pub outputs: Vec<TensorInfo>,
    /// 模型是否已加载
    pub is_loaded: bool,
}

/// 张量信息
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct TensorInfo {
    /// 张量名称
    pub name: String,
    /// 数据类型
    pub dtype: String,
    /// 形状（-1表示动态维度）
    pub shape: Vec<i64>,
}

/// 健康检查响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct HealthResponse {
    /// 服务状态
    pub status: String,
    /// 服务版本
    pub version: String,
    /// 模型状态
    pub model_status: String,
}

/// 错误响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ErrorResponse {
    /// 错误代码
    pub error_code: String,
    /// 错误消息
    pub message: String,
    /// 请求ID
    pub request_id: Option<String>,
}

/// 模型加载请求
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct LoadModelRequest {
    /// 模型文件路径
    pub model_path: String,
    /// 模型名称（可选）
    pub model_name: Option<String>,
}

/// 模型加载响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct LoadModelResponse {
    /// 是否成功
    pub success: bool,
    /// 消息
    pub message: String,
    /// 模型信息
    pub model_info: Option<ModelInfo>,
}

impl TensorData {
    /// 创建新的张量数据
    pub fn new(dtype: String, shape: Vec<i64>, data: Vec<f32>) -> Self {
        Self { dtype, shape, data }
    }

    /// 验证数据完整性
    pub fn validate(&self) -> Result<(), String> {
        let expected_size: i64 = self.shape.iter().product();
        if expected_size as usize != self.data.len() {
            return Err(format!(
                "Data size mismatch: expected {}, got {}",
                expected_size,
                self.data.len()
            ));
        }
        Ok(())
    }
}

// ==================== 动态模型配置相关结构体 ====================

/// 模型配置文件结构（对应config.json）
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ModelConfig {
    /// 模型基本信息
    pub model: ModelMetadata,
    /// 输入规格
    pub inputs: Vec<InputSpec>,
    /// 输出规格
    pub outputs: Vec<OutputSpec>,
}

/// 模型元数据
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ModelMetadata {
    /// 模型名称
    pub name: String,
    /// 模型版本
    pub version: String,
    /// 模型描述
    pub description: String,
}

/// 输入规格（兼容两种格式）
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct InputSpec {
    /// 特征名称（兼容 feature 和 feature_name 字段）
    #[serde(alias = "feature_name")]
    pub feature: String,
    /// 最小值
    pub min: f64,
    /// 最大值
    pub max: f64,
    /// 描述
    pub description: String,
}

/// 输出规格
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct OutputSpec {
    /// 目标名称
    pub target: String,
    /// 最小值
    pub min: f64,
    /// 最大值
    pub max: f64,
    /// 描述
    pub description: String,
}

/// 注册的模型信息
#[derive(Debug, Clone)]
pub struct RegisteredModel {
    /// UUID（文件夹名称）
    pub uuid: String,
    /// 模型配置
    pub config: ModelConfig,
    /// ONNX文件路径
    pub onnx_path: String,
    /// 配置文件路径
    pub config_path: String,
}

/// 动态模型推理请求
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct DynamicInferenceRequest {
    /// 输入数据，键为特征名称，值为特征值
    pub inputs: HashMap<String, f64>,
}

/// 动态模型推理响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct DynamicInferenceResponse {
    /// 输出数据，键为目标名称，值为预测值
    pub outputs: HashMap<String, f64>,
    /// 推理耗时（毫秒）
    pub inference_time_ms: f64,
    /// 请求ID
    pub request_id: String,
    /// 模型UUID
    pub model_uuid: String,
}

/// 模型配置信息响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ModelConfigResponse {
    /// 模型UUID
    pub uuid: String,
    /// 模型配置
    pub config: ModelConfig,
    /// 模型状态
    pub status: String,
    /// 是否已加载
    pub is_loaded: bool,
}

impl Default for HealthResponse {
    fn default() -> Self {
        Self {
            status: "healthy".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            model_status: "unknown".to_string(),
        }
    }
}
