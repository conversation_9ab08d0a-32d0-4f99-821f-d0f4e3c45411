mod config;
mod dynamic_router;
mod handlers;
mod model_scanner;
mod models;
mod onnx_engine;
mod swagger;

use anyhow::Result;
use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tower::ServiceBuilder;
use tower_http::cors::CorsLayer;
use tracing::{error, info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use utoipa::{Modify, OpenApi};
use utoipa_swagger_ui::SwaggerUi;

use config::Config;
use dynamic_router::{create_dynamic_routes, DynamicModelManager};
use handlers::{get_model_info, health_check, inference, load_model, unload_model, AppState};
use model_scanner::ModelScanner;
use onnx_engine::OnnxEngine;
use swagger::{ApiDoc, ServerAddon};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "onnx_service=info,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // 加载配置
    let config = Config::from_env()?;
    info!("Configuration loaded successfully");
    info!("Server will start on: {}", config.server_address());

    // 创建ONNX引擎
    let engine = Arc::new(RwLock::new(OnnxEngine::new(config.onnx.clone())));

    // 如果配置了模型路径，尝试自动加载模型
    if std::path::Path::new(&config.model.path).exists() {
        info!("Auto-loading model from: {}", config.model.path);
        let mut engine_guard = engine.write().await;
        match engine_guard
            .load_model(&config.model.path, Some(config.model.name.clone()))
            .await
        {
            Ok(model_info) => {
                info!("Model auto-loaded successfully: {}", model_info.name);
            }
            Err(e) => {
                error!("Failed to auto-load model: {}", e);
                info!("Server will start without a pre-loaded model");
            }
        }
    } else {
        info!(
            "Model file not found at configured path: {}",
            config.model.path
        );
        info!("Server will start without a pre-loaded model");
    }

    // 创建应用状态
    let app_state = AppState { engine };

    // 扫描并注册动态模型
    let dynamic_manager = DynamicModelManager::new(config.onnx.clone());
    let mut scanner = ModelScanner::new("./models");

    match scanner.scan_and_register().await {
        Ok(uuids) => {
            info!("发现 {} 个UUID模型文件夹", uuids.len());

            // 注册每个发现的模型
            for uuid in uuids {
                if let Some(model) = scanner.get_model_by_uuid(&uuid) {
                    match dynamic_manager.register_model(model.clone()).await {
                        Ok(_) => info!("成功注册动态模型: {}", uuid),
                        Err(e) => error!("注册动态模型失败 {}: {}", uuid, e),
                    }
                }
            }
        }
        Err(e) => {
            warn!("模型扫描失败: {}", e);
            info!("服务将在没有动态模型的情况下启动");
        }
    }

    // 创建路由
    let app = create_router(app_state, &config, dynamic_manager);

    // 启动服务器
    let listener = tokio::net::TcpListener::bind(&config.server_address()).await?;
    info!("🚀 ONNX Service started on {}", config.server_address());
    info!(
        "📖 Swagger UI available at: http://{}/swagger-ui/",
        config.server_address()
    );

    axum::serve(listener, app).await?;

    Ok(())
}

fn create_router(
    app_state: AppState,
    config: &Config,
    dynamic_manager: DynamicModelManager,
) -> Router {
    // 创建动态配置的OpenAPI文档
    let mut openapi = ApiDoc::openapi();
    ServerAddon::new(config.server_address()).modify(&mut openapi);

    // 模型管理路由
    let model_routes = Router::new()
        .route("/info", get(get_model_info))
        .route("/load", post(load_model))
        .route("/unload", post(unload_model));

    // 动态模型路由
    let dynamic_routes = create_dynamic_routes(dynamic_manager);

    // 主路由
    Router::new()
        .merge(SwaggerUi::new("/swagger-ui").url("/api-docs/openapi.json", openapi))
        .route("/health", get(health_check))
        .route("/inference", post(inference))
        .nest("/model", model_routes)
        .nest("/models", dynamic_routes) // 动态模型路由前缀
        .with_state(app_state)
        .layer(
            ServiceBuilder::new()
                .layer(CorsLayer::permissive())
                .into_inner(),
        )
}
